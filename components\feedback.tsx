import React, { useState } from "react";
import { ArrowUp } from "lucide-react";
import moment from "moment";
import { patchFeedbackApi } from "@/lib/api";

type QueryItem = {
  feedback: string[];
  created_at: string;
  urgency: string;
  source: string;
  question: string;
  ai_answer: string;
  tags: string[];
  _id: string;
};

interface Query {
  data: QueryItem;
}

function Feedback({
  data,
  setOpen,
}: {
  data: QueryItem;
  setOpen: (open: boolean) => void;
}) {
  console.log("Feedback data", data);
  const [tags, setTags] = useState(data.tags || []);
  const [feedback, setFeedback] = useState("");
  const [msg, setMsg] = useState("");

  const handleFeedbackSubmit = async () => {
    try {
      const res = await patchFeedbackApi(data._id, {
        question: data.question,
        feedback_text: feedback,
      });
      setMsg("Feedback submitted successfully");
      setFeedback("");
      setOpen(false); // Close the modal after submission
      window.location.reload();
    } catch (error) {
      console.error("Failed to submit feedback");
    }
  };
  return (
    <div className="py-5">
      <div className="flex gap-5 items-center">
        <p>
          {" "}
          <span className="font-semibold">Date:</span>{" "}
          {moment.utc(data.created_at).local().format("YYYY-MM-DD")}
        </p>
        <p>
          {" "}
          <span className="font-semibold">Source:</span> {data.source}
        </p>
      </div>
      <div className="flex flex-col gap-1 mt-4">
        <h1 className="text-[#3B4154] text-lg font-semibold">Query</h1>
        <p>{data.question}</p>
      </div>
      <div className="flex flex-col gap-1 mt-4">
        <h1 className="text-[#3B4154] text-lg font-semibold">AI Response</h1>
        <div className="border-2 border-blue-300 rounded-lg p-3 mt-2">
          <p>{data.ai_answer || "No AI response available."}</p>
        </div>
      </div>
      <div className="flex gap-2 mt-4">
        <h2 className="font-medium text-md">Tags</h2>
        <div>
          {tags.map((tag, index) => (
            <span
              key={index}
              className="inline-block bg-gray-100 text-gray-500 text-sm mr-2 px-2.5 py-0.5 rounded"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>
      <div className="flex flex-col gap-2 mt-4">
        <div>
          <h1 className="font-medium text-md">Feedback</h1>
          <p className="text-sm text-gray-500">
            All feedback will be logged, preserved & utilized to fine-tune the
            AI's future replies
          </p>
        </div>
        <div className="flex justify-between items-center gap-2 mt-2 border border-gray-300 rounded-lg p-2">
          <input
            type="text"
            placeholder="Enter comment here..."
            className="outline-none"
            value={feedback}
            onChange={(e) => setFeedback(e.target.value)}
          />
          <button onClick={handleFeedbackSubmit}>
            <ArrowUp className="bg-teal-500 text-white rounded-full p-1 w-8 h-8" />
          </button>
        </div>
        {msg && <p className="text-green-500 text-sm mt-2">{msg}</p>}

        <div className="flex flex-col gap-3 mt-4 mb-5">
          {data.feedback.length > 0 ? (
            data.feedback.map((text, index) => (
              <div key={index} className="flex gap-2 items-start">
                <div>
                  <p className="flex justify-center items-center text-white rounded-full p-1 w-8 h-8">
                    <img src="/ross-feedback-icon.png" alt="S" />
                  </p>
                </div>
                <div className="flex flex-col gap-1">
                  <p>Ross Senior Sales Engineer</p>
                  <p key={index} className="text-md text-gray-500">
                    {text}
                  </p>
                </div>
              </div>
            ))
          ) : (
            <p className="text-sm text-gray-500">No feedback available.</p>
          )}
        </div>
      </div>
    </div>
  );
}

export default Feedback;
